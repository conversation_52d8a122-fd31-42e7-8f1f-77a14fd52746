<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marker Clearing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Marker Clearing Test</h1>
        <p>This test verifies the enhanced marker clearing functionality addresses the contract violation issues.</p>

        <div class="test-section">
            <h3>Test 1: Normal Clearing (Map Available)</h3>
            <button onclick="testNormalClearing()">Run Test</button>
            <div id="test1-results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Clearing Without Map (Contract Violation)</h3>
            <button onclick="testClearingWithoutMap()">Run Test</button>
            <div id="test2-results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Cleaning Up Invalid Entries</h3>
            <button onclick="testInvalidEntryCleanup()">Run Test</button>
            <div id="test3-results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>Test 4: Mixed Scenario (Valid + Invalid Entries)</h3>
            <button onclick="testMixedScenario()">Run Test</button>
            <div id="test4-results" class="results"></div>
        </div>
    </div>

    <script type="module">
        // Mock Leaflet and dependencies
        window.L = {
            map: () => ({
                removeLayer: (marker) => {
                    if (marker.shouldFail) {
                        throw new Error('Mock removal failure');
                    }
                }
            })
        };

        // Mock logger
        const logger = {
            debug: (msg) => console.log(`[DEBUG] ${msg}`),
            warn: (msg) => console.warn(`[WARN] ${msg}`),
            error: (msg, err) => console.error(`[ERROR] ${msg}`, err)
        };

        // Mock MapRenderer with the enhanced _clearMarkerCollection method
        class MockMapRenderer {
            _clearMarkerCollection(collection, map, collectionName, cleanupInvalidEntries = false) {
                let removedCount = 0;
                let invalidEntriesCount = 0;
                const successfullyRemovedIds = [];
                const invalidEntryIds = [];

                // Iterate through the collection and attempt to remove each marker
                collection.forEach((entry, id) => {
                    try {
                        if (entry.marker && map) {
                            map.removeLayer(entry.marker);
                            // Only track as successfully removed if removeLayer didn't throw
                            successfullyRemovedIds.push(id);
                            removedCount++;
                        } else if (cleanupInvalidEntries && !entry.marker) {
                            // Track invalid entries (those without markers) for cleanup
                            invalidEntryIds.push(id);
                            invalidEntriesCount++;
                        }
                    } catch (e) {
                        logger.error(`Error removing ${collectionName} marker ${id}:`, e);
                        // Continue processing other markers even if this one failed
                    }
                });

                // Only delete markers from collection that were successfully removed from map
                successfullyRemovedIds.forEach(id => {
                    collection.delete(id);
                });

                // Optionally clean up invalid entries
                if (cleanupInvalidEntries) {
                    invalidEntryIds.forEach(id => {
                        collection.delete(id);
                    });
                }

                const stats = {
                    removedCount,
                    invalidEntriesCount,
                    remainingCount: collection.size,
                    wasFullyCleared: collection.size === 0
                };

                logger.debug(`Cleared ${collectionName} markers: ${removedCount} removed from map, ${invalidEntriesCount} invalid entries ${cleanupInvalidEntries ? 'cleaned up' : 'left'}, ${stats.remainingCount} remaining`);
                
                // Warn if collection is not empty after clearing (potential contract violation)
                if (!stats.wasFullyCleared && !cleanupInvalidEntries) {
                    logger.warn(`${collectionName} marker collection not fully cleared: ${stats.remainingCount} entries remain (map unavailable or entries without markers)`);
                }

                return stats;
            }

            clearAllPokemonMarkers(cleanupInvalidEntries = false) {
                const pokemonMarkers = window.testPokemonMarkers;
                const map = window.testMap;
                
                try {
                    return this._clearMarkerCollection(pokemonMarkers, map, 'Pokemon', cleanupInvalidEntries);
                } catch (e) {
                    logger.error('Error clearing Pokemon markers:', e);
                    return { removedCount: 0, invalidEntriesCount: 0, remainingCount: pokemonMarkers?.size || 0, wasFullyCleared: false };
                }
            }
        }

        const mockRenderer = new MockMapRenderer();

        // Test functions
        window.testNormalClearing = function() {
            const results = document.getElementById('test1-results');
            results.textContent = 'Running test...\n';

            // Setup test data
            window.testPokemonMarkers = new Map();
            window.testMap = L.map();

            // Add valid markers
            window.testPokemonMarkers.set('pokemon1', { marker: { id: 'marker1' } });
            window.testPokemonMarkers.set('pokemon2', { marker: { id: 'marker2' } });
            window.testPokemonMarkers.set('pokemon3', { marker: { id: 'marker3' } });

            results.textContent += `Initial collection size: ${window.testPokemonMarkers.size}\n`;

            // Clear markers
            const stats = mockRenderer.clearAllPokemonMarkers();

            results.textContent += `\nClearing results:\n`;
            results.textContent += `- Removed from map: ${stats.removedCount}\n`;
            results.textContent += `- Invalid entries: ${stats.invalidEntriesCount}\n`;
            results.textContent += `- Remaining in collection: ${stats.remainingCount}\n`;
            results.textContent += `- Fully cleared: ${stats.wasFullyCleared}\n`;

            if (stats.wasFullyCleared && stats.removedCount === 3) {
                results.textContent += '\n✅ TEST PASSED: All markers cleared successfully';
                results.className = 'results success';
            } else {
                results.textContent += '\n❌ TEST FAILED: Expected all markers to be cleared';
                results.className = 'results error';
            }
        };

        window.testClearingWithoutMap = function() {
            const results = document.getElementById('test2-results');
            results.textContent = 'Running test...\n';

            // Setup test data without map
            window.testPokemonMarkers = new Map();
            window.testMap = null; // No map available

            // Add valid markers
            window.testPokemonMarkers.set('pokemon1', { marker: { id: 'marker1' } });
            window.testPokemonMarkers.set('pokemon2', { marker: { id: 'marker2' } });

            results.textContent += `Initial collection size: ${window.testPokemonMarkers.size}\n`;

            // Clear markers without cleanup
            const stats = mockRenderer.clearAllPokemonMarkers(false);

            results.textContent += `\nClearing results (no cleanup):\n`;
            results.textContent += `- Removed from map: ${stats.removedCount}\n`;
            results.textContent += `- Invalid entries: ${stats.invalidEntriesCount}\n`;
            results.textContent += `- Remaining in collection: ${stats.remainingCount}\n`;
            results.textContent += `- Fully cleared: ${stats.wasFullyCleared}\n`;

            if (!stats.wasFullyCleared && stats.remainingCount === 2) {
                results.textContent += '\n✅ TEST PASSED: Contract violation detected - entries remain when map unavailable';
                results.className = 'results warning';
            } else {
                results.textContent += '\n❌ TEST FAILED: Expected entries to remain when map unavailable';
                results.className = 'results error';
            }
        };

        window.testInvalidEntryCleanup = function() {
            const results = document.getElementById('test3-results');
            results.textContent = 'Running test...\n';

            // Setup test data
            window.testPokemonMarkers = new Map();
            window.testMap = L.map();

            // Add invalid entries (no marker property)
            window.testPokemonMarkers.set('pokemon1', { /* no marker */ });
            window.testPokemonMarkers.set('pokemon2', { marker: null });
            window.testPokemonMarkers.set('pokemon3', { marker: undefined });

            results.textContent += `Initial collection size: ${window.testPokemonMarkers.size}\n`;

            // Clear markers with cleanup
            const stats = mockRenderer.clearAllPokemonMarkers(true);

            results.textContent += `\nClearing results (with cleanup):\n`;
            results.textContent += `- Removed from map: ${stats.removedCount}\n`;
            results.textContent += `- Invalid entries cleaned: ${stats.invalidEntriesCount}\n`;
            results.textContent += `- Remaining in collection: ${stats.remainingCount}\n`;
            results.textContent += `- Fully cleared: ${stats.wasFullyCleared}\n`;

            if (stats.wasFullyCleared && stats.invalidEntriesCount === 3) {
                results.textContent += '\n✅ TEST PASSED: Invalid entries cleaned up successfully';
                results.className = 'results success';
            } else {
                results.textContent += '\n❌ TEST FAILED: Expected all invalid entries to be cleaned up';
                results.className = 'results error';
            }
        };

        window.testMixedScenario = function() {
            const results = document.getElementById('test4-results');
            results.textContent = 'Running test...\n';

            // Setup test data
            window.testPokemonMarkers = new Map();
            window.testMap = L.map();

            // Add mixed entries
            window.testPokemonMarkers.set('valid1', { marker: { id: 'marker1' } });
            window.testPokemonMarkers.set('invalid1', { /* no marker */ });
            window.testPokemonMarkers.set('valid2', { marker: { id: 'marker2' } });
            window.testPokemonMarkers.set('invalid2', { marker: null });
            window.testPokemonMarkers.set('failing', { marker: { id: 'marker3', shouldFail: true } });

            results.textContent += `Initial collection size: ${window.testPokemonMarkers.size}\n`;

            // Clear markers with cleanup
            const stats = mockRenderer.clearAllPokemonMarkers(true);

            results.textContent += `\nClearing results (mixed scenario):\n`;
            results.textContent += `- Removed from map: ${stats.removedCount}\n`;
            results.textContent += `- Invalid entries cleaned: ${stats.invalidEntriesCount}\n`;
            results.textContent += `- Remaining in collection: ${stats.remainingCount}\n`;
            results.textContent += `- Fully cleared: ${stats.wasFullyCleared}\n`;

            // Should have 2 valid removed, 2 invalid cleaned, 1 failed removal remaining
            if (stats.removedCount === 2 && stats.invalidEntriesCount === 2 && stats.remainingCount === 1) {
                results.textContent += '\n✅ TEST PASSED: Mixed scenario handled correctly';
                results.className = 'results success';
            } else {
                results.textContent += '\n❌ TEST FAILED: Mixed scenario not handled as expected';
                results.className = 'results error';
            }
        };
    </script>
</body>
</html>
